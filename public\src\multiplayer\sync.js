// sync.js
// Handles game state sync via WebSocket
import { onMessage, send } from './socket.js';

let stateListeners = [];

export function subscribeToState(fn) {
  stateListeners.push(fn);
  return fn; // Return the function so it can be used to unsubscribe
}

export function unsubscribeFromState(fn) {
  const index = stateListeners.indexOf(fn);
  if (index > -1) {
    stateListeners.splice(index, 1);
  }
}

// Call this once to start listening for state updates from server
onMessage((msg) => {
  if (msg.type === 'state') {
    stateListeners.forEach(fn => fn(msg.state));
  }
});

// Send player action to server
export function sendAction(action) {
  console.log('[Sync] sendAction called with:', action);
  console.log('[Sync] About to call send...');

  try {
    send({ type: 'action', action });
    console.log('[Sync] send() completed successfully');
  } catch (error) {
    console.error('[Sync] Error in send():', error);
    throw error;
  }
}

// Send chat message to server
export function sendChat(text) {
  send({ type: 'chat', text });
}
