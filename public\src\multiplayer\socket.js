// socket.js
// Simple WebSocket client for Bomberman

let socket = null;
let listeners = [];
let playerId = localStorage.getItem('playerId') || null;
let messageQueue = []; // Queue for messages sent before socket is ready
let autoHandshakeEnabled = true; // Control auto-handshake behavior

export function connect(url) {
  socket = new WebSocket(url);
  socket.onopen = () => {

    // Flush any queued messages
    while (messageQueue.length > 0) {
      const queuedMsg = messageQueue.shift();
      socket.send(JSON.stringify(queuedMsg));
    }

    // Auto-handshake: only if enabled and we have a valid session
    if (autoHandshakeEnabled && hasValidSession()) {
      const storedNickname = localStorage.getItem('nickname');
      send({ type: 'nickname', nickname: storedNickname });
    }
  };
  socket.onmessage = (event) => {
    const msg = JSON.parse(event.data);
    if (msg.type === 'playerId' && msg.id) {
      playerId = msg.id;
      localStorage.setItem('playerId', playerId);
    }
    listeners.forEach(fn => fn(msg));
  };
  socket.onclose = () => {
    // Clear any queued messages on disconnect
    messageQueue = [];
    // Removed auto-reconnect to prevent flickering
    // setTimeout(() => connect(url), 2000);
  };

  socket.onerror = (error) => {
    console.error('[Socket] WebSocket error:', error);
  };
}

export function send(msg) {
  console.log('[Socket] send() called with:', msg);
  console.log('[Socket] Socket state:', socket ? socket.readyState : 'null');
  console.log('[Socket] WebSocket.OPEN constant:', WebSocket.OPEN);

  // Add playerId to nickname messages
  if (msg.type === 'nickname') {
    msg.playerId = playerId;
  }

  if (socket && socket.readyState === WebSocket.OPEN) {
    console.log('[Socket] Sending message via WebSocket...');
    socket.send(JSON.stringify(msg));
    console.log('[Socket] Message sent successfully');
  } else {
    console.log('[Socket] Socket not ready, queuing message...');
    // Queue the message if socket isn't ready yet
    messageQueue.push(msg);
    console.log('[Socket] Message queued, queue length:', messageQueue.length);
  }
}

export function onMessage(fn) {
  listeners.push(fn);
}

export function isConnected() {
  return socket && socket.readyState === WebSocket.OPEN;
}

export function getPlayerId() {
  return playerId;
}

// Utility function to clear stored session data (for testing/debugging)
export function clearSession() {
  localStorage.removeItem('playerId');
  localStorage.removeItem('nickname');
  localStorage.removeItem('sessionTimestamp');
  playerId = null;
  messageQueue = [];
}

// Check if we have a stored session
export function hasStoredSession() {
  return !!(localStorage.getItem('nickname') && localStorage.getItem('playerId'));
}

// Check if we have a valid session (not expired)
function hasValidSession() {
  const nickname = localStorage.getItem('nickname');
  const playerId = localStorage.getItem('playerId');
  const sessionTimestamp = localStorage.getItem('sessionTimestamp');

  if (!nickname || !playerId || !sessionTimestamp) {
    return false;
  }

  // Session expires after 1 hour
  const sessionAge = Date.now() - parseInt(sessionTimestamp);
  const SESSION_TIMEOUT = 60 * 60 * 1000; // 1 hour

  if (sessionAge > SESSION_TIMEOUT) {
    clearSession();
    return false;
  }

  return true;
}

// Disable auto-handshake (when user wants to enter new nickname)
export function disableAutoHandshake() {
  autoHandshakeEnabled = false;
}

// Enable auto-handshake (for normal reconnects)
export function enableAutoHandshake() {
  autoHandshakeEnabled = true;
}

// Start a new session (clear old data and disable auto-handshake)
export function startNewSession() {
  clearSession();
  disableAutoHandshake();
}
